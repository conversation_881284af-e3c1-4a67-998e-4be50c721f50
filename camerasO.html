<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8"/>
  <title>Cameras</title>
  <meta name="viewport" content="width=device-width, initial-scale=1"/>
  <style>
    :root { --gap: 12px; --card-bg: #111; --text: #eee; --muted: #bbb; --border: #333; }
    body{margin:0;font-family:system-ui,Segoe UI,Arial,sans-serif;background:#0a0a0a;color:var(--text);}
    header{display:flex;gap:var(--gap);align-items:center;padding:10px 12px;border-bottom:1px solid var(--border);position:sticky;top:0;background:#0a0a0a;z-index:10}
    select,input,button{background:#0f0f0f;color:var(--text);border:1px solid var(--border);border-radius:6px;padding:8px}
    button{cursor:pointer}
    #grid{display:grid;gap:var(--gap);padding:var(--gap)}
    @media (min-width: 1024px){ #grid{grid-template-columns: repeat(3, 1fr);} }
    @media (min-width: 1400px){ #grid{grid-template-columns: repeat(4, 1fr);} }
    .card{border:1px solid var(--border);border-radius:10px;overflow:hidden;background:var(--card-bg)}
    .meta{display:flex;justify-content:space-between;gap:8px;padding:8px 10px;border-top:1px solid var(--border);font-size:13px;color:var(--muted)}
    .title{font-weight:600;color:#fff}
    .loc{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;max-width:45%}
    video{width:100%;height:auto;display:block;background:#000}
    .banner{padding:10px 12px;color:#ddd}
    .err{padding:10px;color:#f66;font-size:13px}
    a{color:#7fb3ff}

    /* Admin panel */
    .admin-wrap{position:fixed;top:0;right:0;height:100%;width:420px;max-width:92vw;background:#0b0b0b;border-left:1px solid var(--border);transform:translateX(100%);transition:transform .2s ease;z-index:50;display:flex;flex-direction:column;}
    .admin-wrap.open{transform:translateX(0)}
    .admin-head{display:flex;justify-content:space-between;align-items:center;padding:10px;border-bottom:1px solid var(--border)}
    .admin-body{padding:12px;overflow:auto;display:flex;flex-direction:column;gap:10px}
    .row{display:flex;gap:8px;align-items:center;flex-wrap:wrap}
    .form-grid{display:grid;gap:8px;grid-template-columns:1fr}
    .msg{font-size:13px}
  </style>
  <script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
</head>
<body>
  <header>
    <label>Customer:
      <select id="customerSel"></select>
    </label>
    <input id="q" placeholder="Search camera, location, city, area"/>
    <span id="count" class="banner"></span>
    <button id="adminBtn" title="Edit device attributes">Admin</button>
  </header>

  <div id="grid"></div>

  <!-- Admin drawer -->
  <aside id="admin" class="admin-wrap" aria-hidden="true">
    <div class="admin-head">
      <strong>Admin: Edit attributes</strong>
      <button id="adminClose">Close</button>
    </div>
    <div class="admin-body">
      <div class="row">
        <label>Admin token <input id="admToken" type="password" placeholder="Required to save"/></label>
      </div>
      <div class="row">
        <label>Customer
          <select id="adminCustomerSel"></select>
        </label>
        <label>Device
          <select id="deviceSel"></select>
        </label>
      </div>
      <div class="form-grid">
        <label>Stream URL <input id="a_stream_url" placeholder="rtsp://user:pass@ip:554/..."></label>
        <label>Location <input id="a_location" placeholder="HQ Lobby / Site name"></label>
        <label>Area <input id="a_area"></label>
        <label>City <input id="a_city"></label>
        <label>Category <input id="a_category" placeholder="Retail / Industry / ..."></label>
        <label>Working hours <input id="a_working_hours" placeholder="08:00-17:00"></label>
      </div>
      <div class="row">
        <button id="adminSave">Save</button>
        <span id="adminMsg" class="msg"></span>
      </div>
    </div>
  </aside>

  <script>
    const params = new URLSearchParams(location.search);
    let presetCustomer = params.get('customer');

    let all = [];        // flat array of camera records from cameras.json
    let customers = [];  // unique customers

    // Fetch JSON produced by Node-RED:
    fetch('./cameras.json', {cache:'no-store'})
      .then(r => r.json())
      .then(data => {
        if (Array.isArray(data)) all = data;
        else if (Array.isArray(data.customers)) {
          data.customers.forEach(c => c.cameras.forEach(cam => all.push({...cam, customer: c.customer})));
        } else {
          all = data.items || [];
        }
        customers = [...new Set(all.map(x => x.customer))].sort((a,b)=>a.localeCompare(b));
        buildUI();
        buildAdminUI();
        applyFilter();
      })
      .catch(err => {
        document.body.insertAdjacentHTML('beforeend',
          `<div class="err">Failed to load cameras.json: ${escapeHtml(String(err))}</div>`);
      });

    function buildUI(){
      const sel = document.getElementById('customerSel');
      sel.innerHTML = `<option value="__all__">All customers</option>` +
        customers.map(c => `<option>${escapeHtml(c)}</option>`).join('');
      if (presetCustomer && customers.includes(presetCustomer)) sel.value = presetCustomer;
      sel.addEventListener('change', applyFilter);
      document.getElementById('q').addEventListener('input', debounce(applyFilter, 250));
      document.getElementById('adminBtn').addEventListener('click', () => toggleAdmin(true));
      document.getElementById('adminClose').addEventListener('click', () => toggleAdmin(false));
      document.getElementById('adminSave').addEventListener('click', adminSave);
    }

    function applyFilter(){
      const customer = document.getElementById('customerSel').value;
      const q = document.getElementById('q').value.trim().toLowerCase();
      const filtered = all.filter(x => {
        const custOk = (customer === '__all__') || x.customer === customer;
        const txt = [
          x.deviceName||'', x.location||'', x.city||'', x.area||'', x.category||''
        ].join(' ').toLowerCase();
        const qOk = !q || txt.includes(q);
        return custOk && qOk && !!x.hls; // must have hls url to render a playable card
      });
      render(filtered);
      const p = new URLSearchParams(location.search);
      if (customer && customer !== '__all__') p.set('customer', customer); else p.delete('customer');
      history.replaceState(null, '', location.pathname + '?' + p.toString());
    }

    function render(items){
      const grid = document.getElementById('grid');
      grid.innerHTML = '';
      document.getElementById('count').textContent = `${items.length} camera(s)`;
      items.forEach(cam => {
        const card = document.createElement('div');
        card.className = 'card';
        card.innerHTML = `
          <video muted playsinline controls preload="none" poster="" data-hls="${cam.hls}"></video>
          <div class="meta">
            <div class="title">${escapeHtml(cam.deviceName || cam.path || '')}</div>
            <div class="loc" title="${escapeHtml((cam.location||cam.city||'')+'')}">
              ${escapeHtml(cam.location || cam.city || '')}
            </div>
          </div>`;
        grid.appendChild(card);
      });
      lazyAttach();
    }

    function escapeHtml(s){ return s?.replace(/[&<>"']/g, m => ({'&':'&amp;','<':'&lt;','>':'&gt;','"':'&quot;',"'":'&#39;'}[m])) || ''; }

    function lazyAttach(){
      const vids = [...document.querySelectorAll('video')];
      const io = new IntersectionObserver(entries => {
        entries.forEach(e => { if (e.isIntersecting) { attach(e.target); io.unobserve(e.target); } });
      }, { rootMargin: '100px' });
      vids.forEach(v => io.observe(v));
    }

    function attach(video){
      const url = video.dataset.hls;
      if (!url) return;
      if (video.canPlayType('application/vnd.apple.mpegurl')) {
        video.src = url;
        video.play().catch(()=>{});
      } else if (window.Hls && window.Hls.isSupported()) {
        const hls = new Hls({ lowLatencyMode: true, backBufferLength: 30 });
        hls.loadSource(url);
        hls.attachMedia(video);
        video.play().catch(()=>{});
      } else {
        const msg = document.createElement('div');
        msg.className = 'err';
        msg.innerHTML = `This browser can't play HLS. <a href="${url}" target="_blank">Open stream</a>`;
        video.replaceWith(msg);
      }
    }

    function debounce(fn, ms){ let t; return (...a)=>{ clearTimeout(t); t=setTimeout(()=>fn(...a), ms); }; }

    /* ===== Admin panel ===== */
    function toggleAdmin(open){ 
      const el = document.getElementById('admin'); 
      el.classList.toggle('open', !!open);
      el.setAttribute('aria-hidden', open ? 'false' : 'true');
      if (open) fillAdminSelectors();
    }

    function buildAdminUI(){
      document.getElementById('adminCustomerSel').addEventListener('change', fillDeviceSelector);
      document.getElementById('deviceSel').addEventListener('change', () => fillForm(getSelectedDevice()));
    }

    function fillAdminSelectors(){
      const csel = document.getElementById('adminCustomerSel');
      csel.innerHTML = customers.map(c => `<option>${escapeHtml(c)}</option>`).join('');
      const mainSel = document.getElementById('customerSel').value;
      if (mainSel && mainSel !== '__all__' && customers.includes(mainSel)) csel.value = mainSel;
      fillDeviceSelector();
    }

    function fillDeviceSelector(){
      const csel = document.getElementById('adminCustomerSel').value;
      const dsel = document.getElementById('deviceSel');
      const list = all.filter(x => x.customer === csel).sort((a,b)=> (a.deviceName||'').localeCompare(b.deviceName||''));
      dsel.innerHTML = list.map(d => `<option value="${d.deviceId}">${escapeHtml(d.deviceName)} (${escapeHtml(d.deviceLabel||'')})</option>`).join('');
      fillForm(getSelectedDevice());
    }

    function getSelectedDevice(){
      const id = document.getElementById('deviceSel').value;
      return all.find(x => x.deviceId === id);
    }

    function fillForm(d){
      if (!d) return;
      setVal('a_stream_url', d.rtsp || '');
      setVal('a_location', d.location || '');
      setVal('a_area', d.area || '');
      setVal('a_city', d.city || '');
      setVal('a_category', d.category || '');
      setVal('a_working_hours', d.working_hours || '');
      document.getElementById('adminMsg').textContent = '';
    }

    function setVal(id, v){ const el = document.getElementById(id); if (el) el.value = v; }
    function getVal(id){ const el = document.getElementById(id); return el ? el.value.trim() : ''; }

    async function adminSave(){
      const d = getSelectedDevice();
      const token = getVal('admToken');
      if (!d) return;
      if (!token){ return showAdminMsg('Enter admin token.', true); }
      const payload = {
        deviceId: d.deviceId,
        stream_url: getVal('a_stream_url'),
        location: getVal('a_location'),
        area: getVal('a_area'),
        city: getVal('a_city'),
        category: getVal('a_category'),
        working_hours: getVal('a_working_hours')
      };
      try{
        const res = await fetch('/attributes/set', {
          method: 'POST',
          headers: { 'Content-Type':'application/json', 'X-Admin-Token': token },
          body: JSON.stringify(payload)
        });
        const js = await res.json().catch(()=>({}));
        if (!res.ok || !js.ok){ throw new Error((js && js.error) || 'Save failed'); }
        showAdminMsg('Saved. JSON will refresh on next update; updating local view now...');
        // Update local record and UI immediately
        d.rtsp = payload.stream_url || d.rtsp;
        d.location = payload.location || d.location;
        d.area = payload.area || d.area;
        d.city = payload.city || d.city;
        d.category = payload.category || d.category;
        d.working_hours = payload.working_hours || d.working_hours;
        // If stream_url added, make sure HLS URL is populated (MediaMTX must have matching path)
        if (d.rtsp && !d.hls) d.hls = `/mtx/hls/cam-${d.deviceId}/index.m3u8`;
        applyFilter();
      }catch(e){
        showAdminMsg('Error: ' + (e.message || e), true);
      }
    }

    function showAdminMsg(txt, isErr=false){
      const el = document.getElementById('adminMsg');
      el.textContent = txt;
      el.style.color = isErr ? '#f66' : '#9f9';
    }
  </script>
</body>
</html>
